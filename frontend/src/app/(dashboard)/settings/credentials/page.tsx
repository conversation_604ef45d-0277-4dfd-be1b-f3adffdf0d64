'use client';

import React, { useEffect, useState, useMemo } from 'react';
import {
  Plus,
  Key,
  Shield,
  Trash2,
  Loader2,
  AlertTriangle,
  Star,
  Settings2,
  Users,
  Sparkles,
  Clock,
  Server,
  Globe,
  Zap,
  MoreHorizontal,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';
import {
  useCredentialProfiles,
  useDeleteCredentialProfile,
  useSetDefaultProfile,
  type CredentialProfile
} from '@/hooks/react-query/mcp/use-credential-profiles';
import { useAllAgentMCPs, useUpdateAgentCustomMCPs } from '@/hooks/react-query/agents/use-agents';
import { EnhancedAddCredentialDialog } from './_components/enhanced-add-credential-dialog';
import { MCPJsonEditorDialog } from './_components/mcp-json-editor-dialog';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { useFeatureFlag } from '@/lib/feature-flags';

interface CredentialProfileCardProps {
  profile: CredentialProfile;
  onDelete: (profileId: string) => void;
  onSetDefault: (profileId: string) => void;
  isDeletingId?: string;
  isSettingDefaultId?: string;
}

const CredentialProfileCard: React.FC<CredentialProfileCardProps> = ({
  profile,
  onDelete,
  onSetDefault,
  isDeletingId,
  isSettingDefaultId
}) => {
  const isDeleting = isDeletingId === profile.profile_id;
  const isSettingDefault = isSettingDefaultId === profile.profile_id;
  const isCustomServer = profile.mcp_qualified_name.startsWith('custom_');

  const getCustomServerType = () => {
    if (profile.mcp_qualified_name.startsWith('custom_sse_')) return 'SSE';
    if (profile.mcp_qualified_name.startsWith('custom_http_')) return 'HTTP';
    if (profile.mcp_qualified_name.startsWith('custom_json_')) return 'JSON';
    return 'Custom';
  };

  const getServerIcon = () => {
    if (isCustomServer) {
      const type = getCustomServerType();
      if (type === 'SSE') return <Globe className="h-3.5 w-3.5" />;
      if (type === 'HTTP') return <Server className="h-3.5 w-3.5" />;
      return <Settings2 className="h-3.5 w-3.5" />;
    }
    return <Key className="h-3.5 w-3.5" />;
  };

  return (
    <Card className={`group transition-all bg-sidebar duration-200 py-0 border-border/60 ${
      profile.is_default
        ? 'ring-1 ring-primary/10 border-primary/10 bg-primary/5'
        : 'hover:border-border hover:bg-accent/20'
    }`}>
      <CardContent className="p-3">
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className={`p-1.5 rounded-md transition-colors ${
                profile.is_default
                  ? 'bg-primary/20 text-primary'
                  : 'bg-muted/70 text-muted-foreground group-hover:bg-muted'
              }`}>
                {getServerIcon()}
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-sm font-medium text-foreground truncate">{profile.profile_name}</h3>
                <p className="text-xs text-muted-foreground truncate">{profile.display_name}</p>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  disabled={isDeleting || isSettingDefault}
                >
                  <MoreHorizontal className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                {!profile.is_default && (
                  <DropdownMenuItem
                    onClick={() => onSetDefault(profile.profile_id)}
                    disabled={isSettingDefault}
                  >
                    {isSettingDefault ? (
                      <Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
                    ) : (
                      <Star className="h-3.5 w-3.5" />
                    )}
                    Set as Default
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => onDelete(profile.profile_id)}
                  disabled={isDeleting}
                  className="text-destructive focus:text-destructive"
                >
                  {isDeleting ? (
                    <Loader2 className="text-destructive h-3.5 w-3.5 animate-spin" />
                  ) : (
                    <Trash2 className="text-destructiveh-3.5 w-3.5" />
                  )}
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center gap-1.5 flex-wrap">
            {profile.is_default && (
              <Badge variant="default" className="text-xs h-5 bg-primary/15 text-primary border-primary/30">
                <Star className="h-2.5 w-2.5 mr-1" />
                Default
              </Badge>
            )}
            {isCustomServer && (
              <Badge variant="outline" className="text-xs h-5 bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
                <Sparkles className="h-2.5 w-2.5 mr-1" />
                {getCustomServerType()}
              </Badge>
            )}
            <Badge variant="secondary" className="text-xs h-5 bg-muted/50 text-muted-foreground">
              {profile.config_keys.length} key{profile.config_keys.length !== 1 ? 's' : ''}
            </Badge>
          </div>

          {/* Last Used */}
          {profile.last_used_at && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="h-2.5 w-2.5" />
              <span>{new Date(profile.last_used_at).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  profileToDelete: CredentialProfile | null;
  onConfirm: () => void;
  isDeleting: boolean;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  onOpenChange,
  profileToDelete,
  onConfirm,
  isDeleting
}) => {
  if (!profileToDelete) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 rounded-lg bg-destructive/10">
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </div>
            Delete Profile
          </DialogTitle>
          <DialogDescription className="text-left">
            Delete <span className="font-medium">"{profileToDelete.profile_name}"</span> for {profileToDelete.display_name}?
            <br />
            <span className="text-destructive text-sm">This action cannot be undone.</span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isDeleting}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm} disabled={isDeleting}>
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function CredentialsPage() {
  const { enabled: customAgentsEnabled, loading: flagLoading } = useFeatureFlag("custom_agents");
  const router = useRouter();
  useEffect(() => {
    if (!flagLoading && !customAgentsEnabled) {
      router.replace("/dashboard");
    }
  }, [flagLoading, customAgentsEnabled, router]);


  const [showAddDialog, setShowAddDialog] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);
  const [profileToDelete, setProfileToDelete] = useState<CredentialProfile | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [showMCPEditor, setShowMCPEditor] = useState(false);
  const [editingMCP, setEditingMCP] = useState<{
    mcp: any;
    agentId: string;
    agentName: string;
    mcpIndex: number;
  } | null>(null);

  const { data: profiles, isLoading, error, refetch } = useCredentialProfiles();
  const deleteProfileMutation = useDeleteCredentialProfile();
  const setDefaultProfileMutation = useSetDefaultProfile();
  const { data: allAgentMCPs, isLoading: isLoadingAgentMCPs, refetch: refetchAgentMCPs } = useAllAgentMCPs();
  const updateAgentCustomMCPsMutation = useUpdateAgentCustomMCPs();



  const handleDelete = (profileId: string) => {
    const profile = profiles?.find(p => p.profile_id === profileId);
    if (profile) {
      setProfileToDelete(profile);
      setShowDeleteDialog(true);
    }
  };

  const confirmDelete = async () => {
    if (!profileToDelete) return;

    setDeletingId(profileToDelete.profile_id);
    try {
      await deleteProfileMutation.mutateAsync(profileToDelete.profile_id);
      toast.success('Profile deleted successfully');
      setShowDeleteDialog(false);
      setProfileToDelete(null);
      refetch();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete profile');
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetDefault = async (profileId: string) => {
    setSettingDefaultId(profileId);
    try {
      await setDefaultProfileMutation.mutateAsync(profileId);
      refetch();
    } catch (error: any) {
      toast.error(error.message || 'Failed to set default profile');
    } finally {
      setSettingDefaultId(null);
    }
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };



  const handleSaveMCP = async (agentId: string, mcpIndex: number, updatedMcp: any) => {
    try {
      // Find the agent and get its current custom_mcps
      const agent = allAgentMCPs?.agents.find(a => a.agent_id === agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // Create a copy of the current custom_mcps and update the specific MCP
      const updatedCustomMcps = [...agent.custom_mcps];
      updatedCustomMcps[mcpIndex] = updatedMcp;

      // Update the agent's custom_mcps
      await updateAgentCustomMCPsMutation.mutateAsync({
        agentId,
        customMcps: updatedCustomMcps
      });

      // Refresh the agent MCPs data
      await refetchAgentMCPs();
    } catch (error) {
      console.error('Error saving MCP:', error);
      throw error;
    }
  };

  // Combine all MCPs across agents with duplicate handling
  const combinedMCPs = useMemo(() => {
    if (!allAgentMCPs?.agents) return [];

    const mcpMap = new Map();

    allAgentMCPs.agents.forEach(agent => {
      agent.custom_mcps.forEach((mcp, index) => {
        const key = `${mcp.name}-${mcp.type}`;
        if (!mcpMap.has(key)) {
          mcpMap.set(key, {
            ...mcp,
            agents: [{
              agent_id: agent.agent_id,
              agent_name: agent.agent_name,
              is_default: agent.is_default,
              mcp_index: index
            }]
          });
        } else {
          // Add this agent to the existing MCP
          mcpMap.get(key).agents.push({
            agent_id: agent.agent_id,
            agent_name: agent.agent_name,
            is_default: agent.is_default,
            mcp_index: index
          });
        }
      });
    });

    return Array.from(mcpMap.values());
  }, [allAgentMCPs]);

  const handleEditCombinedMCP = (combinedMcp: any, agentInfo: any) => {
    setEditingMCP({
      mcp: combinedMcp,
      agentId: agentInfo.agent_id,
      agentName: agentInfo.agent_name,
      mcpIndex: agentInfo.mcp_index
    });
    setShowMCPEditor(true);
  };

  const groupedProfiles = profiles?.reduce((acc, profile) => {
    const key = profile.mcp_qualified_name;
    if (!acc[key]) {
      acc[key] = {
        serverName: profile.display_name,
        qualifiedName: profile.mcp_qualified_name,
        profiles: []
      };
    }
    acc[key].profiles.push(profile);
    return acc;
  }, {} as Record<string, { serverName: string; qualifiedName: string; profiles: CredentialProfile[] }>);

  if (flagLoading) {
    return (
      <div className="h-screen max-w-7xl mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
              <Shield className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-foreground">MCP Credential Profiles</h1>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="p-2 bg-neutral-100 dark:bg-sidebar rounded-2xl overflow-hidden group">
              <div className="h-24 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100">
                <Skeleton className="h-24 w-full rounded-xl" />
              </div>
              <div className="space-y-2 mt-4 mb-4">
                <Skeleton className="h-6 w-32 rounded" />
                <Skeleton className="h-4 w-24 rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!customAgentsEnabled) {
    return null;
  }

  if (error) {
    return (
      <div className="container mx-auto max-w-6xl px-6 py-6">
        <Alert variant="destructive" className="border-destructive/30 bg-destructive/5">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load credential profiles. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl px-6 py-6">
      <div className="space-y-6">
        {/* MCP Server Configurations Section */}
        <div className="space-y-6">
          <div className='w-full space-y-4 bg-gradient-to-b from-primary/10 to-primary/5 border rounded-xl h-60 flex items-center justify-center'>
            <div className="space-y-4">
              <div className="space-y-2 text-center">
                <div className='flex items-center justify-center gap-2'>
                  <Server className='h-6 w-6 text-primary' />
                  <h1 className="text-2xl font-semibold tracking-tight text-foreground">
                    App Configurations
                  </h1>
                </div>
                <p className="text-md text-muted-foreground max-w-2xl">
                  View and edit app configurations from all your agents
                </p>
              </div>
            </div>
          </div>

          {isLoadingAgentMCPs ? (
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden">
                  <div className='p-4'>
                    <Skeleton className="h-12 w-12 rounded-lg" />
                  </div>
                  <div className="p-4">
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-24 rounded" />
                      <Skeleton className="h-4 w-full rounded" />
                      <Skeleton className="h-4 w-3/4 rounded" />
                      <div className="flex gap-1 mt-2">
                        <Skeleton className="h-5 w-12 rounded" />
                        <Skeleton className="h-5 w-16 rounded" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : !allAgentMCPs || allAgentMCPs.total_custom_mcps === 0 ? (
            <Card className="border-dashed border-border/60 bg-muted/20">
              <CardContent className="p-8 text-center">
                <div className="space-y-4">
                  <div className="p-3 rounded-full bg-muted/60 w-fit mx-auto">
                    <Server className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-semibold text-foreground">No apps configured</h3>
                    <p className="text-sm text-muted-foreground">
                      Configure apps in your agents to see them here
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {combinedMCPs.map((combinedMcp, index) => {
                // Get app icon using the same mapping as the carousel
                const getAppIconComponent = () => {
                  if (combinedMcp.app_name) {
                    // Use the same icon mapping as the carousel
                    return getComposioAppIcon({
                      name: combinedMcp.app_name,
                      key: combinedMcp.app_name.toLowerCase().replace(/\s+/g, '_'),
                      icon: undefined
                    });
                  }

                  // Fallback to Server icon for non-app MCPs
                  return Server;
                };

                // Generate a color based on app name
                const getAppColor = () => {
                  const colors = [
                    '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
                    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
                    '#ec4899', '#6366f1', '#14b8a6', '#eab308'
                  ];
                  const hash = combinedMcp.name.split('').reduce((a: number, b: string) => {
                    a = ((a << 5) - a) + b.charCodeAt(0);
                    return a & a;
                  }, 0);
                  return colors[Math.abs(hash) % colors.length];
                };

                return (
                  <div
                    key={`${combinedMcp.name}-${index}`}
                    className="bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden hover:bg-muted/50 transition-all duration-200 cursor-pointer group"
                  >
                    <div className='p-4'>
                      <div
                        className="h-12 w-12 flex items-center justify-center rounded-lg"
                        style={{ backgroundColor: getAppColor() }}
                      >
                        <div className="w-8 h-8 flex items-center justify-center bg-background border border-border rounded-md">
                          {(() => {
                            const IconComponent = getAppIconComponent();
                            return <IconComponent className="h-5 w-5 text-muted-foreground" />;
                          })()}
                        </div>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-foreground font-medium text-lg line-clamp-1 flex-1">
                          {combinedMcp.name}
                        </h3>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {combinedMcp.agents.map((agentInfo: any) => (
                              <DropdownMenuItem
                                key={agentInfo.agent_id}
                                onClick={() => handleEditCombinedMCP(combinedMcp, agentInfo)}
                              >
                                Edit in {agentInfo.agent_name}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="flex gap-1 mb-2">
                        <Badge variant="secondary" className="text-xs">
                          {combinedMcp.type.toUpperCase()}
                        </Badge>
                        {combinedMcp.app_name && (
                          <Badge variant="outline" className="text-xs">
                            {combinedMcp.app_name}
                          </Badge>
                        )}
                      </div>

                      <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                        {combinedMcp.enabledTools.length > 0
                          ? `${combinedMcp.enabledTools.length} tools enabled`
                          : 'No tools enabled'
                        }
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-xs">
                          Used in {combinedMcp.agents.length} agent{combinedMcp.agents.length !== 1 ? 's' : ''}
                        </span>
                        <div className="flex gap-1">
                          {combinedMcp.agents.slice(0, 3).map((agentInfo: any, i: number) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {agentInfo.is_default ? 'Default' : agentInfo.agent_name.slice(0, 8)}
                            </Badge>
                          ))}
                          {combinedMcp.agents.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{combinedMcp.agents.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* MCP Credential Profiles Section - Dropdown */}
        <Collapsible open={expandedSections.has('credential-profiles')} onOpenChange={() => toggleSection('credential-profiles')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-start p-0 h-auto hover:bg-transparent">
              <div className="flex items-center gap-2 w-full p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                <div className="p-1.5 rounded-lg bg-primary/10">
                  <Shield className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1 min-w-0 text-left">
                  <h3 className="font-semibold text-foreground truncate">MCP Credential Profiles</h3>
                  <p className="text-xs text-muted-foreground truncate">Manage authentication profiles for MCP servers</p>
                </div>
                <Badge variant="outline" className="text-xs shrink-0">
                  {profiles?.length || 0}
                </Badge>
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAddDialog(true);
                  }}
                  size="sm"
                  className="h-7 text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add
                </Button>
                {expandedSections.has('credential-profiles') ? (
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3">

        <Alert className="border-primary/30 bg-primary/5">
          <Zap className="h-4 w-4 text-primary" />
          <AlertDescription className="text-sm">
            Create multiple profiles per MCP server for different use cases (teams, organizations, environments).
          </AlertDescription>
        </Alert>

        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="w-8 h-8 rounded-lg"></Skeleton>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32 rounded"></Skeleton>
                    <Skeleton className="h-3 w-24 rounded"></Skeleton>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <Card key={j} className="bg-sidebar border-border/50">
                      <CardContent className="p-3">
                        <div className="animate-pulse space-y-2">
                          <div className="flex items-center gap-2">
                            <Skeleton className="w-6 h-6 rounded"></Skeleton>
                            <div className="space-y-1 flex-1">
                              <Skeleton className="h-3 w-20 rounded"></Skeleton>
                              <Skeleton className="h-2.5 w-16 rounded"></Skeleton>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Skeleton className="h-4 w-12 rounded"></Skeleton>
                            <Skeleton className="h-4 w-8 rounded"></Skeleton>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : !profiles || profiles.length === 0 ? (
          <Card className="border-dashed border-border/60 bg-muted/20">
            <CardContent className="p-8 text-center">
              <div className="space-y-4">
                <div className="p-3 rounded-full bg-muted/60 w-fit mx-auto">
                  <Users className="h-6 w-6 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <h3 className="font-semibold text-foreground">No profiles yet</h3>
                  <p className="text-sm text-muted-foreground">
                    Create your first credential profile to get started
                  </p>
                </div>
                <Button onClick={() => setShowAddDialog(true)} className="h-9">
                  <Plus className="h-4 w-4" />
                  Create First Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {Object.entries(groupedProfiles || {}).map(([qualifiedName, serverGroup]) => {
              const isExpanded = expandedSections.has(qualifiedName);
              return (
                <Collapsible key={qualifiedName} open={isExpanded} onOpenChange={() => toggleSection(qualifiedName)}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" className="w-full justify-start p-0 h-auto hover:bg-transparent">
                      <div className="flex items-center gap-2 w-full p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                        <div className="p-1.5 rounded-lg bg-primary/10">
                          <Settings2 className="h-4 w-4 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0 text-left">
                          <h3 className="font-semibold text-foreground truncate">{serverGroup.serverName}</h3>
                          <p className="text-xs text-muted-foreground font-mono truncate">{serverGroup.qualifiedName}</p>
                        </div>
                        <Badge variant="outline" className="text-xs shrink-0">
                          {serverGroup.profiles.length}
                        </Badge>
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pl-4">
                      {serverGroup.profiles.map((profile) => (
                        <CredentialProfileCard
                          key={profile.profile_id}
                          profile={profile}
                          onDelete={handleDelete}
                          onSetDefault={handleSetDefault}
                          isDeletingId={deletingId}
                          isSettingDefaultId={settingDefaultId}
                        />
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              );
            })}
          </div>
        )}
        </CollapsibleContent>
        </Collapsible>





        <EnhancedAddCredentialDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSuccess={() => refetch()}
        />

        <DeleteConfirmationDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          profileToDelete={profileToDelete}
          onConfirm={confirmDelete}
          isDeleting={!!deletingId}
        />

        <MCPJsonEditorDialog
          open={showMCPEditor}
          onOpenChange={setShowMCPEditor}
          mcp={editingMCP?.mcp || null}
          agentId={editingMCP?.agentId || ''}
          agentName={editingMCP?.agentName || ''}
          mcpIndex={editingMCP?.mcpIndex || 0}
          onSave={handleSaveMCP}
        />
      </div>
    </div>
  );
}
