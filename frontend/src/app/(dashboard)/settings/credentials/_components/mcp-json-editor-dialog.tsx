'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Save, AlertTriangle, Code2 } from 'lucide-react';
import { toast } from 'sonner';

interface MCPConfiguration {
  name: string;
  type: 'json' | 'sse' | 'http';
  config: Record<string, any>;
  enabledTools: string[];
  app_name?: string;
  auth_type?: string;
}

interface MCPJsonEditorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mcp: MCPConfiguration | null;
  agentId: string;
  agentName: string;
  onSave: (agentId: string, mcpIndex: number, updatedMcp: MCPConfiguration) => Promise<void>;
  mcpIndex: number;
}

export function MCPJsonEditorDialog({
  open,
  onOpenChange,
  mcp,
  agentId,
  agentName,
  onSave,
  mcpIndex
}: MCPJsonEditorDialogProps) {
  const [jsonContent, setJsonContent] = useState('');
  const [isValid, setIsValid] = useState(true);
  const [validationError, setValidationError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (mcp && open) {
      setJsonContent(JSON.stringify(mcp, null, 2));
      setIsValid(true);
      setValidationError('');
    }
  }, [mcp, open]);

  const validateJson = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString);
      
      // Validate required fields
      if (!parsed.name || typeof parsed.name !== 'string') {
        throw new Error('Missing or invalid "name" field');
      }
      
      if (!parsed.type || !['json', 'sse', 'http'].includes(parsed.type)) {
        throw new Error('Missing or invalid "type" field (must be json, sse, or http)');
      }
      
      if (!parsed.config || typeof parsed.config !== 'object') {
        throw new Error('Missing or invalid "config" field (must be an object)');
      }
      
      if (!Array.isArray(parsed.enabledTools)) {
        throw new Error('Missing or invalid "enabledTools" field (must be an array)');
      }
      
      setIsValid(true);
      setValidationError('');
      return parsed;
    } catch (error) {
      setIsValid(false);
      setValidationError(error instanceof Error ? error.message : 'Invalid JSON');
      return null;
    }
  };

  const handleJsonChange = (value: string) => {
    setJsonContent(value);
    validateJson(value);
  };

  const handleSave = async () => {
    const parsedMcp = validateJson(jsonContent);
    if (!parsedMcp) {
      toast.error('Please fix validation errors before saving');
      return;
    }

    setIsSaving(true);
    try {
      await onSave(agentId, mcpIndex, parsedMcp);
      toast.success('MCP configuration updated successfully');
      onOpenChange(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to save configuration');
    } finally {
      setIsSaving(false);
    }
  };

  if (!mcp) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Code2 className="h-5 w-5" />
            Edit MCP Configuration
          </DialogTitle>
          <DialogDescription>
            Editing <strong>{mcp.name}</strong> in agent <strong>{agentName}</strong>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary" className="text-xs">
                {mcp.type.toUpperCase()}
              </Badge>
              {mcp.app_name && (
                <Badge variant="outline" className="text-xs">
                  {mcp.app_name}
                </Badge>
              )}
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 space-y-4">
          {!isValid && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Validation Error:</strong> {validationError}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium">JSON Configuration</label>
            <Textarea
              value={jsonContent}
              onChange={(e) => handleJsonChange(e.target.value)}
              className="font-mono text-sm min-h-[400px] resize-none"
              placeholder="Enter JSON configuration..."
            />
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Required fields:</strong> name (string), type (json|sse|http), config (object), enabledTools (array)
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!isValid || isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
